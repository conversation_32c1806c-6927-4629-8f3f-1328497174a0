#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Migration script to help users transition from Gemini CLI to Arien-AI CLI
 * This script:
 * 1. Copies .gemini directory to .arien-ai
 * 2. Renames GEMINI.md files to ARIEN-AI.md
 * 3. Updates environment variable references
 * 4. Provides guidance on next steps
 */

import { existsSync, mkdirSync, copyFileSync, readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join, dirname, basename } from 'path';
import { homedir } from 'os';

const GEMINI_CONFIG_DIR = '.gemini';
const ARIEN_AI_CONFIG_DIR = '.arien-ai';
const GEMINI_MD_FILE = 'GEMINI.md';
const ARIEN_AI_MD_FILE = 'ARIEN-AI.md';

function log(message) {
  console.log(`[MIGRATE] ${message}`);
}

function warn(message) {
  console.warn(`[WARN] ${message}`);
}

function error(message) {
  console.error(`[ERROR] ${message}`);
}

function copyDirectory(src, dest) {
  if (!existsSync(dest)) {
    mkdirSync(dest, { recursive: true });
  }

  const items = readdirSync(src);
  for (const item of items) {
    const srcPath = join(src, item);
    const destPath = join(dest, item);
    
    if (statSync(srcPath).isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      copyFileSync(srcPath, destPath);
      log(`Copied: ${srcPath} -> ${destPath}`);
    }
  }
}

function renameGeminiMdFiles(directory) {
  if (!existsSync(directory)) {
    return;
  }

  const items = readdirSync(directory);
  for (const item of items) {
    const itemPath = join(directory, item);
    
    if (statSync(itemPath).isDirectory()) {
      renameGeminiMdFiles(itemPath);
    } else if (item === GEMINI_MD_FILE || item === '.gemini.md') {
      const newName = item === GEMINI_MD_FILE ? ARIEN_AI_MD_FILE : '.arien-ai.md';
      const newPath = join(directory, newName);
      
      if (!existsSync(newPath)) {
        copyFileSync(itemPath, newPath);
        log(`Renamed: ${itemPath} -> ${newPath}`);
      } else {
        warn(`File already exists, skipping: ${newPath}`);
      }
    }
  }
}

function updateEnvFile(envPath) {
  if (!existsSync(envPath)) {
    return false;
  }

  try {
    let content = readFileSync(envPath, 'utf-8');
    let updated = false;

    // Replace GEMINI_API_KEY with ARIEN_AI_API_KEY
    if (content.includes('GEMINI_API_KEY=') && !content.includes('ARIEN_AI_API_KEY=')) {
      content = content.replace(/GEMINI_API_KEY=/g, 'ARIEN_AI_API_KEY=');
      updated = true;
    }

    // Replace GEMINI_SANDBOX with ARIEN_AI_SANDBOX
    if (content.includes('GEMINI_SANDBOX=') && !content.includes('ARIEN_AI_SANDBOX=')) {
      content = content.replace(/GEMINI_SANDBOX=/g, 'ARIEN_AI_SANDBOX=');
      updated = true;
    }

    // Replace GEMINI_MODEL with ARIEN_AI_MODEL
    if (content.includes('GEMINI_MODEL=') && !content.includes('ARIEN_AI_MODEL=')) {
      content = content.replace(/GEMINI_MODEL=/g, 'ARIEN_AI_MODEL=');
      updated = true;
    }

    if (updated) {
      writeFileSync(envPath, content);
      log(`Updated environment variables in: ${envPath}`);
      return true;
    }
  } catch (err) {
    error(`Failed to update ${envPath}: ${err.message}`);
  }

  return false;
}

function migrateGlobalConfig() {
  const homeDir = homedir();
  const geminiConfigDir = join(homeDir, GEMINI_CONFIG_DIR);
  const arienAiConfigDir = join(homeDir, ARIEN_AI_CONFIG_DIR);

  if (!existsSync(geminiConfigDir)) {
    log('No global .gemini directory found, skipping global migration');
    return;
  }

  if (existsSync(arienAiConfigDir)) {
    warn(`Global ${ARIEN_AI_CONFIG_DIR} directory already exists, skipping global migration`);
    return;
  }

  log(`Migrating global configuration: ${geminiConfigDir} -> ${arienAiConfigDir}`);
  copyDirectory(geminiConfigDir, arienAiConfigDir);

  // Update .env file in the new directory
  const envPath = join(arienAiConfigDir, '.env');
  updateEnvFile(envPath);

  // Rename GEMINI.md files
  renameGeminiMdFiles(arienAiConfigDir);
}

function migrateProjectConfig() {
  const cwd = process.cwd();
  const geminiConfigDir = join(cwd, GEMINI_CONFIG_DIR);
  const arienAiConfigDir = join(cwd, ARIEN_AI_CONFIG_DIR);

  if (!existsSync(geminiConfigDir)) {
    log('No project .gemini directory found, skipping project migration');
    return;
  }

  if (existsSync(arienAiConfigDir)) {
    warn(`Project ${ARIEN_AI_CONFIG_DIR} directory already exists, skipping project migration`);
    return;
  }

  log(`Migrating project configuration: ${geminiConfigDir} -> ${arienAiConfigDir}`);
  copyDirectory(geminiConfigDir, arienAiConfigDir);

  // Update .env file in the new directory
  const envPath = join(arienAiConfigDir, '.env');
  updateEnvFile(envPath);

  // Rename GEMINI.md files in project root
  renameGeminiMdFiles(cwd);
}

function checkEnvironmentVariables() {
  const envVars = ['GEMINI_API_KEY', 'GEMINI_SANDBOX', 'GEMINI_MODEL'];
  const foundVars = envVars.filter(varName => process.env[varName]);

  if (foundVars.length > 0) {
    warn('Found Gemini environment variables in your current session:');
    foundVars.forEach(varName => {
      const newVarName = varName.replace('GEMINI_', 'ARIEN_AI_');
      warn(`  ${varName} -> should be renamed to ${newVarName}`);
    });
    warn('Please update your shell configuration files (.bashrc, .zshrc, etc.)');
  }
}

function printSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('MIGRATION SUMMARY');
  console.log('='.repeat(60));
  console.log('✅ Configuration directories migrated');
  console.log('✅ GEMINI.md files renamed to ARIEN-AI.md');
  console.log('✅ Environment variables updated in .env files');
  console.log('');
  console.log('NEXT STEPS:');
  console.log('1. Update your shell environment variables:');
  console.log('   - GEMINI_API_KEY -> ARIEN_AI_API_KEY');
  console.log('   - GEMINI_SANDBOX -> ARIEN_AI_SANDBOX');
  console.log('   - GEMINI_MODEL -> ARIEN_AI_MODEL');
  console.log('');
  console.log('2. Install the new Arien-AI CLI:');
  console.log('   npm uninstall -g @google/gemini-cli');
  console.log('   npm install -g @arien-ai/arien-ai-cli');
  console.log('');
  console.log('3. Update any scripts or aliases:');
  console.log('   - Change "gemini" command to "arien-ai"');
  console.log('');
  console.log('4. Test the migration:');
  console.log('   arien-ai --version');
  console.log('');
  console.log('The old .gemini directories and GEMINI.md files are preserved');
  console.log('for safety. You can remove them once you verify everything works.');
  console.log('='.repeat(60));
}

function main() {
  console.log('Arien-AI CLI Migration Tool');
  console.log('Migrating from Gemini CLI to Arien-AI CLI...\n');

  try {
    migrateGlobalConfig();
    migrateProjectConfig();
    checkEnvironmentVariables();
    printSummary();
  } catch (err) {
    error(`Migration failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as migrate };
