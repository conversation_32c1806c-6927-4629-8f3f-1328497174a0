steps:
  # Step 1: Install root dependencies (includes workspaces)
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Install Dependencies'
    entrypoint: 'npm'
    args: ['install']

  # Step 2: Update version in root package.json
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Set version in workspace root'
    entrypoint: 'bash'
    args:
      - -c # Use bash -c to allow for command substitution and string manipulation
      - |
        current_version=$(npm pkg get version | sed 's/"//g')
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          new_version="$current_version"
        else
          new_version="${current_version}-rc.$_REVISION"
        fi
        npm pkg set "version=${new_version}"
        echo "Set root package.json version to: ${new_version}"

  # Step 3: Binds the package versions to the version in the repo root's package.json
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Bind package versions to workspace root'
    entrypoint: 'npm'
    args: ['run', 'prerelease:dev'] # This will run prerelease:version and prerelease:deps

  # Step 4: Authenticate for Docker (so we can push images to the artifact registry)
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Authenticate docker'
    entrypoint: 'npm'
    args: ['run', 'auth']

  # Step 5: Build workspace packages
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Build packages'
    entrypoint: 'npm'
    args: ['run', 'build:packages']

  # Step 6: Prepare CLI package.json for publishing
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Prepare @arien-ai/arien-ai-cli and @arien-ai/arien-ai-core packages'
    entrypoint: 'npm'
    args: ['run', 'prepare:packages']
    env:
      - 'ARIEN_AI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Step 7: Build sandbox container image
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Build sandbox Docker image'
    entrypoint: 'npm'
    args: ['run', 'build:sandbox:fast']
    env:
      - 'ARIEN_AI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Step 8: Publish sandbox container image
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Publish sandbox Docker image'
    entrypoint: 'npm'
    args: ['run', 'publish:sandbox']
    env:
      - 'ARIEN_AI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Pre-Step 9: authenticate to npm registry
  # NOTE: when running locally, run this instead (from the `packages/core` directory):
  #   - `npm login --registry https://registry.npmjs.org`
  #   - use a 24hr token
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Setup @arien-ai/arien-ai-core auth token for publishing'
    entrypoint: 'bash'
    args:
      - -c
      - |
        echo "//registry.npmjs.org/:_authToken=$$CORE_PACKAGE_PUBLISH_TOKEN" > $$HOME/.npmrc
    secretEnv: ['CORE_PACKAGE_PUBLISH_TOKEN']

  # Step 9: Publish @arien-ai/arien-ai-core to NPM
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Publish @arien-ai/arien-ai-core package'
    entrypoint: 'bash'
    args:
      - -c
      - |
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          npm publish --workspace=@arien-ai/arien-ai-core --tag=latest
        else
          npm publish --workspace=@arien-ai/arien-ai-core --tag=rc
        fi
    env:
      - 'ARIEN_AI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Pre-Step 10: authenticate to npm registry
  # NOTE: when running locally, run this instead (from the `packages/cli` directory)
  #   - `npm login --registry https://registry.npmjs.org`
  #   - use a 24hr token
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Setup @arien-ai/arien-ai-cli auth token for publishing'
    entrypoint: 'bash'
    args:
      - -c
      - |
        echo "//registry.npmjs.org/:_authToken=$$CLI_PACKAGE_PUBLISH_TOKEN" > $$HOME/.npmrc
    secretEnv: ['CLI_PACKAGE_PUBLISH_TOKEN']

  # Step 10: Publish @arien-ai/arien-ai-cli to NPM
  - name: 'us-west1-docker.pkg.dev/arien-ai-dev/arien-ai-containers/arien-ai-builder'
    id: 'Publish @arien-ai/arien-ai-cli package'
    entrypoint: 'bash'
    args:
      - -c
      - |
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          npm publish --workspace=@arien-ai/arien-ai-cli --tag=latest
        else
          npm publish --workspace=@arien-ai/arien-ai-cli --tag=rc
        fi
    env:
      - 'ARIEN_AI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

options:
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
  dynamicSubstitutions: true

availableSecrets:
  secretManager:
    - versionName: ${_CLI_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME}
      env: 'CLI_PACKAGE_PUBLISH_TOKEN'
    - versionName: ${_CORE_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME}
      env: 'CORE_PACKAGE_PUBLISH_TOKEN'

substitutions:
  _REVISION: '0'
  _OFFICIAL_RELEASE: 'false'
  _CONTAINER_TOOL: 'docker'
  _SANDBOX_IMAGE_REGISTRY: ''
  _SANDBOX_IMAGE_NAME: ''
  _CLI_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME: ''
  _CORE_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME: ''
